// Test script for tool-enabled implementation
// This script tests the key functions without starting the full bot

import { HustleIncognitoClient } from 'hustle-incognito';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const HUSTLE_API_KEY = process.env.HUSTLE_API_KEY;

if (!HUSTLE_API_KEY) {
  console.error('❌ HUSTLE_API_KEY not found in environment variables');
  process.exit(1);
}

// Test the enhanced client configuration
console.log('🧪 Testing enhanced HustleIncognitoClient configuration...');

try {
  const hustleClient = new HustleIncognitoClient({
    apiKey: HUSTLE_API_KEY,
    debug: process.env.NODE_ENV !== 'production',
    // Enhanced tool enablement configuration
    enableFunctionCalling: true,
    enableExternalTools: true,
    toolExecutionMode: 'auto',
    enableTools: true,
    toolboxEnabled: true,
    config: {
      timeout: 120000,
      retries: 2,
      retryDelay: 2000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'TelegramBot/1.0',
        'X-Enable-Tools': 'true',
        'X-Toolbox-Access': 'full'
      },
      tools: 'all',
      toolConfig: {
        enableWebSearch: true,
        enableBlockchainTools: true,
        enableMarketData: true,
        enableMemoryTools: true,
        maxToolExecutionTime: 30000
      }
    }
  });

  console.log('✅ HustleIncognitoClient created successfully');

  // Test plain text formatting function
  console.log('\n🧪 Testing plain text formatting...');

  function formatAsPlainText(text) {
    if (!text || typeof text !== 'string') return text;

    return text
      .replace(/\*\*(.*?)\*\*/g, '$1')
      .replace(/\*(.*?)\*/g, '$1')
      .replace(/__(.*?)__/g, '$1')
      .replace(/_(.*?)_/g, '$1')
      .replace(/`(.*?)`/g, '$1')
      .replace(/```[\s\S]*?```/g, (match) => {
        return match.replace(/```[\w]*\n?/g, '').replace(/```/g, '');
      })
      .replace(/#{1,6}\s*(.*)/g, '$1')
      .replace(/\[(.*?)\]\(.*?\)/g, '$1')
      .replace(/!\[(.*?)\]\(.*?\)/g, '$1')
      .replace(/^\s*[-*+]\s+/gm, '• ')
      .replace(/^\s*\d+\.\s+/gm, '')
      .replace(/^\s*>\s+/gm, '')
      .replace(/<[^>]*>/g, '')
      .replace(/\n{3,}/g, '\n\n')
      .trim();
  }

  const testMarkdown = `**Bold text** and *italic text*

  # Header

  - List item 1
  - List item 2

  [Link text](https://example.com)

  \`inline code\`

  \`\`\`javascript
  console.log('code block');
  \`\`\``;

  const plainText = formatAsPlainText(testMarkdown);
  console.log('Original markdown:', testMarkdown);
  console.log('Converted to plain text:', plainText);
  console.log('✅ Plain text formatting works correctly');

  // Test a simple chat call with tool enablement
  console.log('\n🧪 Testing tool-enabled chat call...');

  const testResponse = await hustleClient.chat([{
    role: 'user',
    content: 'Hello, can you tell me what tools you have access to?'
  }], {
    vaultId: 'test-vault',
    enableTools: true,
    allowFunctionCalls: true,
    toolboxAccess: 'full',
    executionMode: 'auto',
    maxToolCalls: 10,
    toolTimeout: 30000
  });

  console.log('✅ Tool-enabled chat call successful');
  console.log('Response:', typeof testResponse === 'string' ? testResponse : testResponse.content);

} catch (error) {
  console.error('❌ Test failed:', error.message);
  console.error('Full error:', error);
}

console.log('\n🎉 Testing completed!');