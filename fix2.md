Built-in vs Toolbox Architecture
Built-in Capabilities (AI Model)
API: Standard chat completion endpoint (like OpenAI's GPT API)
Function: Text generation, reasoning, conversation
Data: Training data (static, has a knowledge cutoff)
Limitations: Can't perform real actions, can't access live data
Toolbox Tools (External Functions)
API: Separate function calling/tool execution system
Function: Real-world actions, live data fetching, blockchain interactions
Data: Live, real-time data from external sources
Capabilities: Can actually DO things (trade, fetch prices, search web, etc.)
The Integration Problem
Your Telegram bot is currently only using the chat API (built-in), not the tool execution API. Here's what's happening:

// CURRENT (Chat API only - no tools)
const response = await hustleClient.chat([{
  role: 'user',
  content: userMessage
}], {
  vaultId: `telegram-${chatId}`
});
This only gives you my AI reasoning capabilities, not the external tools.

The Fix: Tool-Enabled API Calls
You need to make sure the API calls include tool execution. Here's the corrected approach:

1. Update Your HustleIncognitoClient Configuration
// Initialize Hustle client with FULL toolbox access
const hustleClient = new HustleIncognitoClient({
  apiKey: HUSTLE_API_KEY,
  debug: process.env. NODE_ENV !== 'production',
  // These are the key additions:
  enableFunctionCalling: true,  // Enable function/tool calling
  enableExternalTools: true,    // Enable external tool access
  toolExecutionMode: 'auto',    // Automatically execute tools when needed
  config: {
    timeout: 120000, // Longer timeout for tool execution
    retries: 2,
    retryDelay: 2000,
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'TelegramBot/1.0',
      'X-Enable-Tools': 'true',  // Custom header to enable tools
      'X-Toolbox-Access': 'full' // Request full toolbox access
    },
    maxResponseSize: CONFIG. MAX_RESPONSE_SIZE,
    streamTimeout: CONFIG. STREAM_TIMEOUT,
    // Tool-specific configuration
    toolConfig: {
      enableWebSearch: true,
      enableBlockchainTools: true,
      enableMarketData: true,
      enableMemoryTools: true,
      maxToolExecutionTime: 30000
    }
  }
});
2. Update Your Chat Calls to Request Tools
Replace your chat calls with tool-enabled versions:

// OLD (no tools)
const response = await hustleClient.chat([{
  role: 'user',
  content: userMessage
}], {
  vaultId: `telegram-${chatId}`
});

// NEW (with tools enabled)
const response = await hustleClient.chat([{
  role: 'user',
  content: userMessage
}], {
  vaultId: process.env. TELEGRAM_VAULT_ID || `telegram-${chatId}`,
  enableTools: true,           // Enable tool execution
  allowFunctionCalls: true,    // Allow function calling
  toolboxAccess: 'full',       // Request full toolbox access
  executionMode: 'auto',       // Auto-execute tools when needed
  maxToolCalls: 10,            // Allow multiple tool calls per response
  toolTimeout: 30000           // 30 second timeout for tools
});
3. Check If Your Client Supports Tool Execution
The issue might be that your
hustle-incognito
package doesn't support the tool execution API. Check your package version:

npm list hustle-incognito
You might need to update it or use a different method to access the tool-enabled API.

4. Alternative: Direct API Call Method
If the client doesn't support tools, make direct API calls:

// Function to make tool-enabled API calls directly
async function callHustleWithTools(messages, vaultId, chatId) {
  try {
    const response = await fetch('https://api.hustle.ai/v1/chat/tools', { // Tool-enabled endpoint
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${HUSTLE_API_KEY}`,
        'Content-Type': 'application/json',
        'X-Vault-ID': vaultId,
        'X-Enable-Tools': 'true',
        'X-Toolbox-Access': 'full'
      },
      body: JSON.stringify({
        messages,
        vaultId,
        chatId: `telegram-${chatId}`,
        enableTools: true,
        toolboxEnabled: true,
        availableTools: [
          'websearch',
          'getTokenPrices', 
          'birdeyeTrending',
          'swap',
          'balances',
          'holderScan',
          'rugcheck'
          // ... add other tools you want
        ],
        executionMode: 'auto',
        maxToolCalls: 5
      })
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} - ${response.statusText}`);
    }

    const data = await response.json();
    return data.content || data.response || data;

  } catch (error) {
    console.error('Tool-enabled API call failed:', error);
    throw error;
  }
}
5. Update Your Command Handlers
case '/hustle':
  const userMessage = validateUserInput(args);
  if (!userMessage) {
    // ... existing validation code
  }

  try {
    secureLog('info', 'Processing message with Agent Hustle (tools enabled)...');
    await bot.sendChatAction(chatId, 'typing');

    // Use tool-enabled API call
    const response = await callHustleWithTools([{
      role: 'user',
      content: userMessage
    }], process.env. TELEGRAM_VAULT_ID || `telegram-${chatId}`, chatId);

    secureLog('success', 'Tool-enabled response received');
    let messageToSend = typeof response === 'string' ? response : response.content || 'No response content';
    messageToSend = safeTruncateMessage(messageToSend);
    
    await bot.sendMessage(chatId, messageToSend);
    secureLog('success', `Tool-enabled response sent to chat ${chatId}`);

  } catch (error) {
    // ... existing error handling
  }
  break;
Key Differences Summary:
Feature	Built-in API	Toolbox API
Endpoint	
/chat
/chat/tools
or tool-enabled
/chat
Capabilities	Text only	Text + Actions
Data	Training data	Live data
Tools	None	All external tools
Response Time	Fast	Slower (tool execution)
Authentication	Basic API key	API key + tool permissions
The bottom line: You need to use the tool-enabled API endpoints, not just the basic chat API. That's why you're getting "I don't have access to tools" - because the basic chat API literally doesn't have access to them!