# Rollback Plan for Tool-Enabled Hustle API Implementation

## Overview
This document provides comprehensive rollback procedures for the tool-enabled Hustle API implementation changes.

## Backup Files Created
- `app.js.backup-[timestamp]` - Original app.js before modifications
- `package.json.backup-[timestamp]` - Original package.json before modifications
- `scheduled_tasks.json.backup-[timestamp]` - Original scheduled tasks configuration

## Current State Documentation (Before Changes)

### Package Versions
- hustle-incognito: 0.1.3 (latest available)
- node-telegram-bot-api: 0.66.0
- dotenv: 16.5.0
- node-cron: 4.0.5

### Current HustleIncognitoClient Configuration
```javascript
const hustleClient = new HustleIncognitoClient({
  apiKey: HUSTLE_API_KEY,
  debug: process.env.NODE_ENV !== 'production',
  enableTools: true, // Enable external tool access
  toolboxEnabled: true, // Explicitly enable toolbox
  onError: (error) => {
    secureLog('error', error.message);
  },
  config: {
    timeout: 60000,
    retries: 2,
    retryDelay: 2000,
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'TelegramBot/1.0'
    },
    maxResponseSize: CONFIG.MAX_RESPONSE_SIZE,
    streamTimeout: CONFIG.STREAM_TIMEOUT,
    tools: 'all' // Enable all available tools
  }
});
```

### Current Chat Call Pattern
```javascript
const response = await hustleClient.chat([{
  role: 'user',
  content: userMessage
}], {
  vaultId: `telegram-${chatId}`
});
```

## Rollback Procedures

### Level 1: Quick Rollback (File Restoration)
If issues occur immediately after implementation:

1. **Stop the bot process**
   ```bash
   # Find and kill the node process
   tasklist /FI "IMAGENAME eq node.exe"
   taskkill /PID [process_id] /F
   ```

2. **Restore original files**
   ```bash
   copy app.js.backup-[timestamp] app.js
   copy package.json.backup-[timestamp] package.json
   copy scheduled_tasks.json.backup-[timestamp] scheduled_tasks.json
   ```

3. **Restart the bot**
   ```bash
   npm start
   ```

### Level 2: Selective Rollback (Configuration Only)
If only client configuration needs to be reverted:

1. **Revert HustleIncognitoClient configuration** to original state (lines 174-194 in app.js)
2. **Revert chat call parameters** to basic format (lines 304-309 in app.js)
3. **Remove any new functions** added for tool-enabled calls

### Level 3: Package Rollback
If package issues occur:

1. **Clear node_modules and reinstall**
   ```bash
   rmdir /s node_modules
   del package-lock.json
   npm install
   ```

2. **Downgrade specific packages if needed**
   ```bash
   npm install hustle-incognito@0.1.2
   ```

## Verification Steps After Rollback

1. **Test basic bot functionality**
   - Send `/start` command
   - Send `/help` command
   - Verify bot responds correctly

2. **Test hustle command**
   - Send `/hustle Hello` command
   - Verify response is received (even if tools don't work)

3. **Test streaming command**
   - Send `/hustlestream Tell me a joke`
   - Verify streaming response works

4. **Check automated tasks**
   - Verify scheduled tasks are still running
   - Check logs for any errors

## Emergency Contacts & Resources

- **Project Repository**: Current working directory
- **Backup Location**: Same directory with timestamp suffixes
- **Log Files**: Console output and secureLog entries
- **Configuration**: .env file (not backed up for security)

## Risk Assessment

### Low Risk Changes
- Adding new configuration parameters
- Adding new optional headers
- Extending timeout values

### Medium Risk Changes
- Modifying existing chat call parameters
- Adding new functions for API calls
- Changing response processing logic

### High Risk Changes
- Modifying core client initialization
- Changing streaming implementation
- Altering automated task execution

## Success Criteria for Implementation

- All existing functionality continues to work
- Tool access is properly enabled
- No breaking changes to user experience
- Automated tasks continue to function
- Error handling remains robust

## Notes
- Always test in development environment first
- Monitor logs closely after implementation
- Have this rollback plan readily available
- Document any additional changes made during implementation