'use strict'

// CONFIG moved to the top, to be absolutely sure it's globally accessible
const CONFIG = {
  MAX_MESSAGE_SIZE: 4096, // Telegram's message size limit
  STREAM_TIMEOUT: 300000, // 5 minutes in milliseconds
  MAX_RESPONSE_SIZE: 1024 * 1024 // 1MB
};

// Streaming configuration constants
const STREAMING_CONFIG = {
  UPDATE_INTERVAL: 1000, // Update every 1 second minimum
  MIN_CHUNK_SIZE: 50, // Minimum characters before update
  MAX_TELEGRAM_LENGTH: 4096, // Telegram message length limit
  TOOL_TIMEOUT: 30000, // 30 seconds timeout for tools
  MAX_TOOL_CALLS: 10, // Maximum tool calls per response
  CHUNK_BATCH_SIZE: 200, // Update message every N characters
  STATUS_MESSAGES: {
    thinking: '🔄 Processing your request...',
    toolRunning: '🔧 Using tool: ',
    toolsRunning: '🔧 Running: ',
    streamComplete: '✅ Stream completed',
    streamError: '❌ Streaming error: ',
    truncated: '\n\n⚠️ Response truncated for live view...',
    lengthWarning: '\n\n📝 *Response was truncated due to length limits.*\nUse /hustle for shorter responses or break your question into parts.'
  }
};

import TelegramBot from 'node-telegram-bot-api';
import { HustleIncognitoClient } from 'hustle-incognito';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import cron from 'node-cron';
import fs from 'fs/promises';
import path from 'path';

// Set timezone for consistent timestamps
process.env.TZ = process.env.TZ || 'America/New_York';
console.log('Server timezone:', process.env.TZ);
console.log('Current server time:', new Date().toLocaleString('en-US', { timeZone: process.env.TZ }));

// Get directory name for ES module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Path for storing scheduled tasks
const TASKS_FILE_PATH = path.join(__dirname, 'scheduled_tasks.json');

// Load environment variables with specific path
dotenv.config({ path: `${__dirname}/.env` });

// --- HELPER FUNCTIONS & CONFIGURATION ORDERING ---

// Basic environment variable sanitization
function sanitizeEnvVar(value, name) {
  if (!value) return null;
  
  // Remove whitespace
  const cleaned = value.trim();
  
  // Basic validation
  if (cleaned.length < 1) {
    console.warn(`⚠️ Warning: ${name} is empty after sanitization`);
    return null;
  }
  
  return cleaned;
}

// Secure logging helper
function secureLog(type, message, data = null) {
  const timestamp = new Date().toLocaleString('en-US', { timeZone: process.env.TZ });
  switch(type) {
    case 'command':
      console.log(`[${timestamp}] 📥 Command: ${data.command} in chat ${data.chatId}`);
      break;
    case 'error':
      console.error(`[${timestamp}] ❌ Error: ${message}`);
      break;
    case 'info':
      console.log(`[${timestamp}] ℹ️ ${message}`);
      break;
    case 'success':
      console.log(`[${timestamp}] ✅ ${message}`);
      break;
  }
}

// Helper to truncate messages if needed
function safeTruncateMessage(message, maxLength = CONFIG.MAX_MESSAGE_SIZE) {
  if (typeof message !== 'string') return 'Invalid message format';
  if (message.length <= maxLength) return message;

  return message.slice(0, maxLength - 3) + '...';
}

// Helper function for safe message editing with fallback strategies
async function safeEditMessage(bot, chatId, messageId, text, options = {}) {
  try {
    return await bot.editMessageText(text, {
      chat_id: chatId,
      message_id: messageId,
      ...options
    });
  } catch (error) {
    // Handle common Telegram API errors gracefully
    if (error.message.includes('message is not modified')) {
      // Message content is the same, ignore this error
      return null;
    }

    if (error.message.includes('parse')) {
      // Try without parse mode if parsing fails
      try {
        return await bot.editMessageText(text, {
          chat_id: chatId,
          message_id: messageId,
          // Remove parse_mode and other formatting options
          ...Object.fromEntries(Object.entries(options).filter(([key]) => key !== 'parse_mode'))
        });
      } catch (fallbackError) {
        secureLog('error', `Message edit fallback failed: ${fallbackError.message}`);
        throw fallbackError;
      }
    }

    if (error.message.includes('message to edit not found') ||
        error.message.includes('message can\'t be edited')) {
      // Message is too old or can't be edited, this is expected behavior
      secureLog('info', 'Message edit failed: message too old or not editable');
      return null;
    }

    // For other errors, log and re-throw
    secureLog('error', `Message edit error: ${error.message}`);
    throw error;
  }
}

// Helper function to escape Markdown characters for Telegram
function escapeMarkdown(text) {
  if (typeof text !== 'string') return '';
  // For parse_mode: 'Markdown' (not MarkdownV2), we mainly need to escape: _, *, `, [
  // Other characters like ., !, -, etc., are generally fine unless part of a more complex structure
  // not typically generated by simple task names/prompts.
  const escapeChars = /[_*`[\]]/g; // More targeted for parse_mode: Markdown
  return text.replace(escapeChars, '\\$&'); // Use $& to refer to the matched string directly
}

// Validate environment variables
const TELEGRAM_BOT_TOKEN = sanitizeEnvVar(process.env.TELEGRAM_BOT_TOKEN, 'TELEGRAM_BOT_TOKEN');
const HUSTLE_API_KEY = sanitizeEnvVar(process.env.HUSTLE_API_KEY, 'HUSTLE_API_KEY');
const ADMIN_USER_IDS_STRING = sanitizeEnvVar(process.env.ADMIN_USER_IDS, 'ADMIN_USER_IDS');

const adminUserIds = ADMIN_USER_IDS_STRING ? ADMIN_USER_IDS_STRING.split(',').map(id => parseInt(id.trim(), 10)) : [];

// More secure environment check
console.log('\nEnvironment check:');
console.log('Required environment variables:', 
  Object.entries({
    TELEGRAM_BOT_TOKEN: !!TELEGRAM_BOT_TOKEN,
    HUSTLE_API_KEY: !!HUSTLE_API_KEY
  }).map(([key, present]) => 
    `\n- ${key}: ${present ? '✅' : '❌'}`
  ).join('')
);

if (!TELEGRAM_BOT_TOKEN || !HUSTLE_API_KEY) {
  console.error('\n❌ Error: Missing required environment variables');
  console.error('Please check your .env file and ensure both TELEGRAM_BOT_TOKEN and HUSTLE_API_KEY are set');
  if (adminUserIds.length === 0) {
    console.warn("⚠️ Warning: ADMIN_USER_IDS is not set or empty in .env. Admin commands for automated tasks will not be available to any user.");
  }
  process.exit(1);
}

// Create a bot instance with updated polling configuration
const bot = new TelegramBot(TELEGRAM_BOT_TOKEN, {
  polling: true // Simplified polling configuration
});

// Message deduplication cache
const processedMessages = new Set();
const MESSAGE_CACHE_TIMEOUT = 5000; // 5 seconds

// In-memory store for scheduled tasks
let scheduledTasks = {}; // Format: { taskName: { cronJob, schedule, targetGroupId, prompts, running, createdBy } }

// Store bot info globally
let botInfo = null;

// Helper function to clean command text
function cleanCommand(command) {
  // Remove bot username from command if present
  if (botInfo && command.includes('@')) {
    const [baseCommand, botUsername] = command.split('@');
    if (botUsername === botInfo.username) {
      return baseCommand;
    }
    // If command is for a different bot, return null
    return null;
  }
  return command;
}

// Helper function to check if a message has been processed
function isMessageProcessed(msg) {
  const messageId = `${msg.chat.id}:${msg.message_id}`;
  if (processedMessages.has(messageId)) {
    return true;
  }
  processedMessages.add(messageId);
  setTimeout(() => processedMessages.delete(messageId), MESSAGE_CACHE_TIMEOUT);
  return false;
}

// Basic input validation
function validateUserInput(input, maxLength = 4096) {
  if (!input || typeof input !== 'string') return '';
  
  // Trim whitespace
  const cleaned = input.trim();
  
  // Check length (Telegram message limit is 4096 characters)
  if (cleaned.length > maxLength) {
    return cleaned.slice(0, maxLength);
  }
  
  return cleaned;
}

console.log('\nInitializing bot...');

// Initialize Hustle client with FULL toolbox access
const hustleClient = new HustleIncognitoClient({
  apiKey: HUSTLE_API_KEY,
  debug: process.env.NODE_ENV !== 'production',
  // Enhanced tool enablement configuration
  enableFunctionCalling: true,  // Enable function/tool calling
  enableExternalTools: true,    // Enable external tool access
  toolExecutionMode: 'auto',    // Automatically execute tools when needed
  enableTools: true, // Enable external tool access
  toolboxEnabled: true, // Explicitly enable toolbox
  onError: (error) => {
    secureLog('error', error.message);
  },
  config: {
    timeout: 120000, // Longer timeout for tool execution (2 minutes)
    retries: 2,
    retryDelay: 2000,
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'TelegramBot/1.0',
      'X-Enable-Tools': 'true',  // Custom header to enable tools
      'X-Toolbox-Access': 'full' // Request full toolbox access
    },
    maxResponseSize: CONFIG.MAX_RESPONSE_SIZE,
    streamTimeout: CONFIG.STREAM_TIMEOUT,
    tools: 'all', // Enable all available tools
    // Tool-specific configuration
    toolConfig: {
      enableWebSearch: true,
      enableBlockchainTools: true,
      enableMarketData: true,
      enableMemoryTools: true,
      maxToolExecutionTime: 30000
    }
  }
});

// Load tasks from file before starting bot logic that might use them
loadTasksFromFile().then(() => {
    // Verify bot connection and store bot info
    bot.getMe().then((info) => {
      botInfo = info;
      console.log('\n✅ Connected to Telegram as:');
      console.log(`Bot Username: @${info.username}`);
      console.log(`Bot Name: ${info.first_name}`);
    }).catch((error) => {
      console.error('\n❌ Failed to connect to Telegram:', error.message);
      process.exit(1);
    });

    // Helper function to check if user is an admin
    function isUserAdmin(userId) {
      return adminUserIds.includes(userId);
    }

    // Function to make tool-enabled API calls directly with enhanced error handling
    async function callHustleWithTools(messages, vaultId, chatId) {
      const startTime = Date.now();
      secureLog('info', `Starting tool-enabled API call for chat ${chatId}`);

      try {
        const requestBody = {
          messages,
          vaultId,
          chatId: `telegram-${chatId}`,
          enableTools: true,
          toolboxEnabled: true,
          availableTools: [
            'websearch',
            'getTokenPrices',
            'birdeyeTrending',
            'swap',
            'balances',
            'holderScan',
            'rugcheck'
          ],
          executionMode: 'auto',
          maxToolCalls: 5
        };

        secureLog('info', `Tool-enabled API request: ${JSON.stringify({...requestBody, messages: '[REDACTED]'})}`);

        const response = await fetch('https://api.hustle.ai/v1/chat/tools', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${HUSTLE_API_KEY}`,
            'Content-Type': 'application/json',
            'X-Vault-ID': vaultId,
            'X-Enable-Tools': 'true',
            'X-Toolbox-Access': 'full'
          },
          body: JSON.stringify(requestBody)
        });

        const duration = Date.now() - startTime;
        secureLog('info', `Tool-enabled API response received in ${duration}ms, status: ${response.status}`);

        if (!response.ok) {
          const errorText = await response.text().catch(() => 'Unable to read error response');
          secureLog('error', `Tool-enabled API Error ${response.status}: ${response.statusText} - ${errorText}`);

          // Provide specific error messages based on status codes
          if (response.status === 401) {
            throw new Error('Authentication failed - check API key');
          } else if (response.status === 403) {
            throw new Error('Tool access forbidden - insufficient permissions');
          } else if (response.status === 429) {
            throw new Error('Rate limit exceeded - please try again later');
          } else if (response.status >= 500) {
            throw new Error('Server error - please try again in a few moments');
          } else {
            throw new Error(`API Error: ${response.status} - ${response.statusText}`);
          }
        }

        const data = await response.json();
        secureLog('success', `Tool-enabled API call completed successfully in ${duration}ms`);

        // Log if tools were used
        if (data.toolCalls && data.toolCalls.length > 0) {
          secureLog('info', `Tools used: ${data.toolCalls.map(tc => tc.name).join(', ')}`);
        }

        return data.content || data.response || data;

      } catch (error) {
        const duration = Date.now() - startTime;
        secureLog('error', `Tool-enabled API call failed after ${duration}ms: ${error.message}`);

        // Enhanced error categorization
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
          throw new Error('Network error - unable to connect to Hustle API');
        } else if (error.message.includes('timeout')) {
          throw new Error('Request timeout - tool execution took too long');
        } else if (error.message.includes('JSON')) {
          throw new Error('Invalid response format from API');
        }

        throw error;
      }
    }

    // Function to format response as plain text (remove markdown/HTML)
    function formatAsPlainText(text) {
      if (!text || typeof text !== 'string') return text;

      return text
        // Remove markdown formatting
        .replace(/\*\*(.*?)\*\*/g, '$1')  // Bold **text**
        .replace(/\*(.*?)\*/g, '$1')      // Italic *text*
        .replace(/__(.*?)__/g, '$1')      // Bold __text__
        .replace(/_(.*?)_/g, '$1')        // Italic _text_
        .replace(/`(.*?)`/g, '$1')        // Inline code `text`
        .replace(/```[\s\S]*?```/g, (match) => {
          // Code blocks - keep content but remove formatting
          return match.replace(/```[\w]*\n?/g, '').replace(/```/g, '');
        })
        .replace(/#{1,6}\s*(.*)/g, '$1')  // Headers # text
        .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Links [text](url)
        .replace(/!\[(.*?)\]\(.*?\)/g, '$1') // Images ![alt](url)
        .replace(/^\s*[-*+]\s+/gm, '• ')  // List items
        .replace(/^\s*\d+\.\s+/gm, '')    // Numbered lists
        .replace(/^\s*>\s+/gm, '')        // Blockquotes
        // Remove HTML tags
        .replace(/<[^>]*>/g, '')
        // Clean up extra whitespace
        .replace(/\n{3,}/g, '\n\n')
        .trim();
    }







    // Central command handler
    async function handleCommand(msg, command, args) {
      if (isMessageProcessed(msg)) {
        secureLog('info', 'Skipping already processed message');
        return;
      }

      const chatId = msg.chat.id;
      secureLog('command', '', { command, chatId });

      // Clean the command
      const cleanedCommand = cleanCommand(command);
      if (!cleanedCommand) {
        secureLog('info', 'Command for different bot, ignoring');
        return;
      }

      switch (cleanedCommand) {
        case '/start':
          const startMessage = `👋 Hello! I'm your Agent Hustle bot.

Available commands:
/start - Show this welcome message
/help - Show available commands
/hustle [message] - Chat with Agent Hustle
/hustlestream [message] - Chat with streaming responses
/streamtest - Test streaming functionality

Try sending: /hustle Tell me about yourself`;

          try {
            await bot.sendMessage(chatId, startMessage);
            secureLog('success', 'Start message sent to chat');
          } catch (error) {
            secureLog('error', `Error sending start message: ${error.message}`);
          }
          break;

        case '/help':
          const helpMessage = `🤖 Available Commands:

/start - Start the bot
/help - Show this help message
/hustle [message] - Chat with Agent Hustle
/hustlestream [message] - Chat with streaming responses
/streamtest - Test streaming functionality

Admin Commands (requires ADMIN_USER_IDS configuration):
/addautotask <name> <cron_schedule> <target_group_id> <prompt1> [prompt2...] - Add a new automated task.
  Example: /addautotask daily_report "0 9 * * *" -100123456789 "Summarize crypto news" "Give me TNSR report"
/listautotasks - List all configured automated tasks.
/toggleautotask <name> - Start or stop an existing automated task.
/deleteautotask <name> - Delete an automated task.
/editautotask <name> [--schedule "new_schedule"] [--groupid <new_group_id>] [--prompts "prompt1" ...] - Edit an existing task.

Examples:
/hustle What can you do?
/hustlestream Tell me a story
/streamtest`;

          try {
            await bot.sendMessage(chatId, helpMessage);
            secureLog('success', 'Help message sent to chat');
          } catch (error) {
            secureLog('error', `Error sending help message: ${error.message}`);
          }
          break;

        case '/hustle':
          const userMessage = validateUserInput(args);
          if (!userMessage) {
            try {
              await bot.sendMessage(chatId, 'Please provide a message after /hustle\nExample: /hustle Tell me about yourself');
              secureLog('success', 'Empty message response sent');
              return;
            } catch (error) {
              secureLog('error', 'Error sending empty message response: ' + error.message);
              return;
            }
          }

          try {
            secureLog('info', 'Processing message with Agent Hustle (tools enabled)...');
            await bot.sendChatAction(chatId, 'typing');

            // Try enhanced tool-enabled client call first
            let response;
            try {
              response = await hustleClient.chat([{
                role: 'user',
                content: userMessage
              }], {
                vaultId: process.env.TELEGRAM_VAULT_ID || `telegram-${chatId}`,
                enableTools: true,           // Enable tool execution
                allowFunctionCalls: true,    // Allow function calling
                toolboxAccess: 'full',       // Request full toolbox access
                executionMode: 'auto',       // Auto-execute tools when needed
                maxToolCalls: 10,            // Allow multiple tool calls per response
                toolTimeout: 30000           // 30 second timeout for tools
              });
              secureLog('success', 'Tool-enabled response received from client');
            } catch (clientError) {
              secureLog('warning', `Client call failed, trying direct API: ${clientError.message}`);
              // Fallback to direct API call
              response = await callHustleWithTools([{
                role: 'user',
                content: userMessage
              }], process.env.TELEGRAM_VAULT_ID || `telegram-${chatId}`, chatId);
              secureLog('success', 'Tool-enabled response received from direct API');
            }

            let messageToSend = typeof response === 'string' ? response : response.content || 'No response content';
            messageToSend = formatAsPlainText(messageToSend); // Convert to plain text
            messageToSend = safeTruncateMessage(messageToSend);

            await bot.sendMessage(chatId, messageToSend);
            secureLog('success', `Tool-enabled response sent to chat ${chatId}`);

          } catch (error) {
            secureLog('error', `Error processing hustle command: ${error.message}`);

            // Enhanced error categorization for better user feedback
            let errorMessage = 'Sorry, I encountered an error processing your request.';

            if (error.message.includes('Authentication failed') || error.message.includes('API key')) {
              errorMessage = '🔐 Authentication error. Please contact the bot administrator.';
            } else if (error.message.includes('Tool access forbidden')) {
              errorMessage = '🚫 Tool access is restricted. Please contact the administrator.';
            } else if (error.message.includes('Rate limit exceeded')) {
              errorMessage = '⏱️ Too many requests. Please wait a moment and try again.';
            } else if (error.message.includes('timeout') || error.message.includes('took too long')) {
              errorMessage = '⏰ Request timed out. The AI tools are taking longer than expected. Please try again.';
            } else if (error.message.includes('Network error') || error.message.includes('unable to connect')) {
              errorMessage = '🌐 Network error. Unable to connect to AI services. Please try again later.';
            } else if (error.message.includes('Server error') || error.message.includes('500')) {
              errorMessage = '🔧 Server error. Please try again in a few moments.';
            } else if (error.message.includes('Invalid response format')) {
              errorMessage = '📄 Received an invalid response. Please try again.';
            }

            try {
              await bot.sendMessage(chatId, errorMessage);
              secureLog('success', 'Enhanced error message sent to user');
            } catch (sendError) {
              secureLog('error', 'Failed to send error message: ' + sendError.message);
            }
          }
          break;

        case '/hustlestream':
          const streamMessage = args.trim();
          if (!streamMessage) {
            try {
              await bot.sendMessage(chatId, 'Please provide a message after /hustlestream\nExample: /hustlestream Tell me a story');
              secureLog('success', 'Empty message response sent');
              return;
            } catch (error) {
              secureLog('error', 'Error sending empty message response: ' + error.message);
              return;
            }
          }

          let streamingMessageId = null;
          let accumulatedResponse = '';
          let lastUpdateTime = 0;
          const UPDATE_INTERVAL = STREAMING_CONFIG.UPDATE_INTERVAL;
          const MAX_TELEGRAM_MESSAGE_LENGTH = STREAMING_CONFIG.MAX_TELEGRAM_LENGTH;

          try {
            // Send initial message that we'll update
            const initialMessage = await bot.sendMessage(chatId, STREAMING_CONFIG.STATUS_MESSAGES.thinking);
            streamingMessageId = initialMessage.message_id;
            secureLog('info', `Streaming: Started with message ID ${streamingMessageId}`);

            // Start streaming request with full toolbox access
            const stream = hustleClient.chatStream({
              vaultId: process.env.TELEGRAM_VAULT_ID || `telegram-${chatId}`,
              messages: [{ role: 'user', content: streamMessage }],
              processChunks: true,
              enableTools: true,           // Enable tools in streaming
              toolboxEnabled: true,        // Ensure toolbox is enabled
              allowFunctionCalls: true,    // Allow function calling
              toolboxAccess: 'full',       // Request full toolbox access
              executionMode: 'auto',       // Auto-execute tools when needed
              maxToolCalls: STREAMING_CONFIG.MAX_TOOL_CALLS,
              toolTimeout: STREAMING_CONFIG.TOOL_TIMEOUT
            });

            let chunkCount = 0;
            let toolCallsInProgress = [];

            // Process stream chunks with live updates
            for await (const chunk of stream) {
              chunkCount++;
              const currentTime = Date.now();

              if (chunk.type === 'text') {
                accumulatedResponse += chunk.value;

                // Update message if enough time has passed or if we have significant content
                if (currentTime - lastUpdateTime > UPDATE_INTERVAL ||
                    accumulatedResponse.length % STREAMING_CONFIG.CHUNK_BATCH_SIZE === 0) {
                  try {
                    let displayMessage = accumulatedResponse;

                    // Add tool status if tools are running
                    if (toolCallsInProgress.length > 0) {
                      displayMessage += `\n\n${STREAMING_CONFIG.STATUS_MESSAGES.toolsRunning}${toolCallsInProgress.join(', ')}...`;
                    }

                    // Truncate if too long for Telegram
                    if (displayMessage.length > MAX_TELEGRAM_MESSAGE_LENGTH - 100) {
                      displayMessage = displayMessage.slice(0, MAX_TELEGRAM_MESSAGE_LENGTH - 100) +
                                     STREAMING_CONFIG.STATUS_MESSAGES.truncated;
                    }

                    await safeEditMessage(bot, chatId, streamingMessageId, displayMessage, {
                      parse_mode: 'Markdown'
                    });

                    lastUpdateTime = currentTime;
                    secureLog('info', `Streaming: Updated message (chunk ${chunkCount})`);

                  } catch (updateError) {
                    // If we can't update, log but continue streaming
                    secureLog('error', `Streaming: Update error: ${updateError.message}`);
                  }
                }
              }

              // Handle tool calls
              if (chunk.type === 'tool_call') {
                const toolName = chunk.value.name;
                secureLog('info', `Streaming: Tool called: ${toolName}`);

                if (!toolCallsInProgress.includes(toolName)) {
                  toolCallsInProgress.push(toolName);

                  // Update message to show tool is running
                  try {
                    let toolMessage = accumulatedResponse +
                                    `\n\n${STREAMING_CONFIG.STATUS_MESSAGES.toolRunning}${toolName}...`;
                    await safeEditMessage(bot, chatId, streamingMessageId, toolMessage);
                  } catch (toolUpdateError) {
                    secureLog('error', `Tool status update error: ${toolUpdateError.message}`);
                  }
                }
              }

              // Handle tool results
              if (chunk.type === 'tool_result') {
                const toolName = chunk.value.tool_call?.name || 'unknown';
                secureLog('info', `Streaming: Tool completed: ${toolName}`);

                // Remove from in-progress list
                toolCallsInProgress = toolCallsInProgress.filter(t => t !== toolName);
              }
            }

            // Final update with complete response
            try {
              let finalMessage = accumulatedResponse || 'No response received';

              // Format as plain text (remove markdown/HTML)
              finalMessage = formatAsPlainText(finalMessage);

              // If response is too long, truncate and offer guidance
              if (finalMessage.length > MAX_TELEGRAM_MESSAGE_LENGTH) {
                const truncatedMessage = finalMessage.slice(0, MAX_TELEGRAM_MESSAGE_LENGTH - 150) +
                  STREAMING_CONFIG.STATUS_MESSAGES.lengthWarning;

                await safeEditMessage(bot, chatId, streamingMessageId, truncatedMessage);
              } else {
                await safeEditMessage(bot, chatId, streamingMessageId, finalMessage);
              }

              secureLog('success', `Streaming: Final response sent (${chunkCount} chunks processed)`);

            } catch (finalError) {
              secureLog('error', `Streaming: Final update error: ${finalError.message}`);
              // Send error as new message if final update fails
              await bot.sendMessage(chatId,
                `${STREAMING_CONFIG.STATUS_MESSAGES.streamComplete} but couldn't update message. Response length: ${accumulatedResponse.length} chars`);
            }

          } catch (error) {
            secureLog('error', 'Streaming: Error processing command: ' + error.message);

            try {
              if (streamingMessageId) {
                await safeEditMessage(bot, chatId, streamingMessageId,
                  `${STREAMING_CONFIG.STATUS_MESSAGES.streamError}${error.message}\n\nTry using /hustle instead for a standard response.`
                );
              } else {
                await bot.sendMessage(chatId,
                  `${STREAMING_CONFIG.STATUS_MESSAGES.streamError}${error.message}\n\nTry using /hustle instead for a standard response.`);
              }
            } catch (sendError) {
              secureLog('error', 'Streaming: Failed to send error message: ' + sendError.message);
            }
          }
          break;

        case '/streamtest':
          try {
            await bot.sendMessage(chatId, '🧪 Starting streaming test...');

            // Test streaming with a simple request
            const testStream = hustleClient.chatStream({
              vaultId: `telegram-${chatId}-test`,
              messages: [{ role: 'user', content: 'Count from 1 to 10 slowly, explaining each number' }],
              processChunks: true,
              enableTools: true,
              toolboxEnabled: true
            });

            let testMessage = await bot.sendMessage(chatId, '🔄 Streaming test in progress...');
            let testResponse = '';

            for await (const chunk of testStream) {
              if (chunk.type === 'text') {
                testResponse += chunk.value;
                await safeEditMessage(bot, chatId, testMessage.message_id,
                  `🧪 Live Stream Test:\n\n${testResponse}`,
                  { parse_mode: 'Markdown' });

                // Small delay to make streaming visible
                await new Promise(resolve => setTimeout(resolve, 500));
              }
            }

            await safeEditMessage(bot, chatId, testMessage.message_id,
              `✅ Streaming Test Complete!\n\n${formatAsPlainText(testResponse)}`);

          } catch (error) {
            await bot.sendMessage(chatId, `❌ Streaming test failed: ${error.message}`);
          }
          break;

        // Admin commands for automated tasks
        case '/addautotask':
          if (!isUserAdmin(msg.from.id)) {
            bot.sendMessage(chatId, "⛔ You are not authorized to use this command.");
            secureLog('info', `Unauthorized /addautotask attempt by user ${msg.from.id}`);
            return;
          }
          // const taskArgs = args.split(' '); // Old problematic line

          // New argument parsing logic
          const matchedArgs = args.match(/"[^"]+"|\S+/g) || [];
          const parsedArgs = matchedArgs.map(arg => {
            if (arg.length >= 2 && arg.startsWith('"') && arg.endsWith('"')) {
              return arg.slice(1, -1); // Remove surrounding quotes
            }
            return arg;
          });

          if (parsedArgs.length < 4) {
            bot.sendMessage(chatId, `Usage: /addautotask <name> <cron_schedule> <target_group_id> <prompt1> [prompt2...]\nParsed args count: ${parsedArgs.length}`);
            return;
          }
          const [taskName, cronSchedule, targetGroupIdStr, ...prompts] = parsedArgs;
          const targetGroupId = parseInt(targetGroupIdStr, 10);

          if (scheduledTasks[taskName]) {
            bot.sendMessage(chatId, `⚠️ Task "${taskName}" already exists. Use /deleteautotask first if you want to redefine it.`);
            return;
          }

          if (!cron.validate(cronSchedule)) {
            bot.sendMessage(chatId, `❌ Invalid cron schedule: "${cronSchedule}". Please use a valid cron format (e.g., "0 9 * * *").`);
            return;
          }
          
          if (isNaN(targetGroupId)) {
            bot.sendMessage(chatId, `❌ Invalid Target Group ID: "${targetGroupIdStr}". Must be a number.`);
            return;
          }

          if (prompts.length === 0) {
            bot.sendMessage(chatId, `❌ You must provide at least one prompt for the task.`);
            return;
          }

          try {
            const job = cron.schedule(cronSchedule, async () => {
              secureLog('info', `Running automated task: ${taskName}`);
              const randomPrompt = prompts[Math.floor(Math.random() * prompts.length)];
              try {
                await bot.sendChatAction(targetGroupId, 'typing').catch(e => secureLog('error', `Error sending chat action to ${targetGroupId} for ${taskName}: ${e.message}`));
                
                const hustleResponse = await hustleClient.chat([{ role: 'user', content: randomPrompt }], {
                  vaultId: `autotask-${taskName}`,
                  enableTools: true,
                  allowFunctionCalls: true,
                  toolboxAccess: 'full',
                  executionMode: 'auto',
                  maxToolCalls: 5,
                  toolTimeout: 30000
                });
                let messageToSend = typeof hustleResponse === 'string' ? hustleResponse : hustleResponse.content || 'No content from Hustle.';
                messageToSend = formatAsPlainText(messageToSend); // Convert to plain text
                messageToSend = safeTruncateMessage(messageToSend);

                await bot.sendMessage(targetGroupId, messageToSend);
                secureLog('success', `Automated task "${taskName}" executed. Prompt: "${randomPrompt}". Response sent to group ${targetGroupId}.`);
              } catch (error) {
                secureLog('error', `Error executing automated task "${taskName}": ${error.message}`);
                try {
                  // Attempt to notify the original admin or a default channel if configured
                  await bot.sendMessage(chatId, `⚠️ Error executing automated task "${taskName}": ${error.message}. Check logs.`);
                } catch (notifyError) {
                  secureLog('error', `Failed to send error notification for task ${taskName}: ${notifyError.message}`);
                }
              }
            }, {
              scheduled: true, // Start the job right away
              timezone: process.env.TZ // Use the server's configured timezone
            });

            scheduledTasks[taskName] = {
              cronJob: job,
              schedule: cronSchedule,
              targetGroupId: targetGroupId,
              prompts: prompts,
              running: true,
              createdBy: msg.from.id
            };
            bot.sendMessage(chatId, `✅ Automated task "${taskName}" added and started with schedule "${cronSchedule}". It will send messages to group ID ${targetGroupId}.`);
            secureLog('success', `Automated task "${taskName}" created by user ${msg.from.id}`);
            saveTasksToFile(); // Save tasks after adding
          } catch (error) {
            bot.sendMessage(chatId, `❌ Error creating task "${taskName}": ${error.message}`);
            secureLog('error', `Error creating task ${taskName}: ${error.message}`);
          }
          break;

        case '/listautotasks':
          if (!isUserAdmin(msg.from.id)) {
            bot.sendMessage(chatId, "⛔ You are not authorized to use this command.");
            secureLog('info', `Unauthorized /listautotasks attempt by user ${msg.from.id}`);
            return;
          }
          let taskListMessage = "📋 Configured Automated Tasks:\n\n";
          if (Object.keys(scheduledTasks).length === 0) {
            taskListMessage += "No tasks configured yet. Use /addautotask to add one.";
          } else {
            for (const name in scheduledTasks) {
              const task = scheduledTasks[name];
              const escapedName = escapeMarkdown(name);
              const escapedSchedule = escapeMarkdown(task.schedule);
              const escapedPrompts = task.prompts.map(p => escapeMarkdown(p)).join(', ');
              
              taskListMessage += `🔹 *Name:* ${escapedName}\n`;
              taskListMessage += `   *Schedule:* \`${escapedSchedule}\`\n`;
              taskListMessage += `   *Target Group ID:* ${task.targetGroupId}\n`;
              taskListMessage += `   *Status:* ${task.running ? '🟢 Running' : '🔴 Stopped'}\n`;
              taskListMessage += `   *Prompts:* \`${escapedPrompts}\`\n\n`;
            }
          }
          // Sending with parse_mode: 'Markdown' as originally intended.
          // If issues persist, we might need to switch to 'MarkdownV2' and ensure the escape function is aligned.
          bot.sendMessage(chatId, taskListMessage, { parse_mode: 'Markdown' }).catch(error => {
            secureLog('error', `Error sending task list: ${error.message}. Raw message: ${taskListMessage}`);
            // Fallback to sending without Markdown if parsing fails
            bot.sendMessage(chatId, "Could not display task list with formatting. Sending raw list:\n\n" + taskListMessage.replace(/[_*[\]()~`>#+\-=|{}.!]/g, '')); // Basic strip for safety
          });
          secureLog('success', `Listed automated tasks for user ${msg.from.id}`);
          break;

        case '/toggleautotask':
          if (!isUserAdmin(msg.from.id)) {
            bot.sendMessage(chatId, "⛔ You are not authorized to use this command.");
            secureLog('info', `Unauthorized /toggleautotask attempt by user ${msg.from.id}`);
            return;
          }
          const taskNameToToggle = args.trim();
          if (!scheduledTasks[taskNameToToggle]) {
            bot.sendMessage(chatId, `❌ Task "${taskNameToToggle}" not found.`);
            return;
          }
          const taskToToggle = scheduledTasks[taskNameToToggle];
          if (taskToToggle.running) {
            taskToToggle.cronJob.stop();
            taskToToggle.running = false;
            bot.sendMessage(chatId, `🔴 Automated task "${taskNameToToggle}" stopped.`);
            secureLog('success', `Automated task "${taskNameToToggle}" stopped by user ${msg.from.id}`);
          } else {
            taskToToggle.cronJob.start();
            taskToToggle.running = true;
            bot.sendMessage(chatId, `🟢 Automated task "${taskNameToToggle}" started.`);
            secureLog('success', `Automated task "${taskNameToToggle}" started by user ${msg.from.id}`);
          }
          saveTasksToFile(); // Save tasks after toggling
          break;

        case '/deleteautotask':
          if (!isUserAdmin(msg.from.id)) {
            bot.sendMessage(chatId, "⛔ You are not authorized to use this command.");
            secureLog('info', `Unauthorized /deleteautotask attempt by user ${msg.from.id}`);
            return;
          }
          const taskNameToDelete = args.trim();
          if (!scheduledTasks[taskNameToDelete]) {
            bot.sendMessage(chatId, `❌ Task "${taskNameToDelete}" not found.`);
            return;
          }
          scheduledTasks[taskNameToDelete].cronJob.stop();
          delete scheduledTasks[taskNameToDelete];
          bot.sendMessage(chatId, `🗑️ Automated task "${taskNameToDelete}" deleted.`);
          secureLog('success', `Automated task "${taskNameToDelete}" deleted by user ${msg.from.id}`);
          saveTasksToFile(); // Save tasks after deleting
          break;

        case '/editautotask':
          if (!isUserAdmin(msg.from.id)) {
            bot.sendMessage(chatId, "⛔ You are not authorized to use this command.");
            secureLog('info', `Unauthorized /editautotask attempt by user ${msg.from.id}`);
            return;
          }

          const editArgsRaw = args.match(/\"[^\"]+\"|\S+/g) || [];
          if (editArgsRaw.length < 1) {
            bot.sendMessage(chatId, "Usage: /editautotask <task_name> [--schedule \"new_schedule\"] [--groupid <new_group_id>] [--prompts \"prompt1\" ...]");
            return;
          }

          const taskNameToEdit = editArgsRaw[0];
          const taskToEdit = scheduledTasks[taskNameToEdit];

          if (!taskToEdit) {
            bot.sendMessage(chatId, `❌ Task "${taskNameToEdit}" not found.`);
            return;
          }

          let newScheduleOpt = null;
          let newTargetGroupIdStrOpt = null;
          let newPromptsOpt = null;
          let changesMadeCount = 0;

          let argIndex = 1;
          while(argIndex < editArgsRaw.length) {
            const currentArg = editArgsRaw[argIndex];
            if (currentArg === '--schedule') {
              if (argIndex + 1 < editArgsRaw.length) {
                newScheduleOpt = editArgsRaw[argIndex + 1].replace(/^"|"$/g, ''); // Remove quotes
                argIndex += 2;
                changesMadeCount++;
              } else {
                bot.sendMessage(chatId, "Error: --schedule flag requires a value."); return;
              }
            } else if (currentArg === '--groupid') {
              if (argIndex + 1 < editArgsRaw.length) {
                newTargetGroupIdStrOpt = editArgsRaw[argIndex + 1].replace(/^"|"$/g, ''); // Remove quotes if any
                argIndex += 2;
                changesMadeCount++;
              } else {
                bot.sendMessage(chatId, "Error: --groupid flag requires a value."); return;
              }
            } else if (currentArg === '--prompts') {
              newPromptsOpt = editArgsRaw.slice(argIndex + 1).map(p => p.replace(/^"|"$/g, '')); // Remove quotes
              changesMadeCount++;
              break; // Prompts consume the rest
            } else {
              bot.sendMessage(chatId, `Error: Unknown flag or misplaced argument: ${currentArg}. Usage: /editautotask <task_name> [--schedule ...] [--groupid ...] [--prompts ...]`);
              return;
            }
          }

          if (changesMadeCount === 0) {
            bot.sendMessage(chatId, `No changes specified for task "${taskNameToEdit}". Use flags like --schedule, --groupid, or --prompts.`);
            return;
          }

          // Prepare new values, using existing if not provided
          let finalSchedule = taskToEdit.schedule;
          let finalTargetGroupId = taskToEdit.targetGroupId;
          let finalPrompts = taskToEdit.prompts;
          let updateMessages = [];

          if (newScheduleOpt !== null) {
            if (!cron.validate(newScheduleOpt)) {
              bot.sendMessage(chatId, `❌ Invalid new cron schedule: "${newScheduleOpt}".`);
              return;
            }
            finalSchedule = newScheduleOpt;
            updateMessages.push(`Schedule updated to "${finalSchedule}"`);
          }

          if (newTargetGroupIdStrOpt !== null) {
            const newGroupId = parseInt(newTargetGroupIdStrOpt, 10);
            if (isNaN(newGroupId)) {
              bot.sendMessage(chatId, `❌ Invalid new Target Group ID: "${newTargetGroupIdStrOpt}". Must be a number.`);
              return;
            }
            finalTargetGroupId = newGroupId;
            updateMessages.push(`Target Group ID updated to ${finalTargetGroupId}`);
          }

          if (newPromptsOpt !== null) {
            if (newPromptsOpt.length === 0) {
              bot.sendMessage(chatId, `❌ New prompts list cannot be empty if --prompts flag is used.`);
              return;
            }
            finalPrompts = newPromptsOpt;
            updateMessages.push(`Prompts updated to: "${finalPrompts.join('", "')}"`);
          }

          try {
            const wasRunning = taskToEdit.running;
            if (taskToEdit.cronJob) {
              taskToEdit.cronJob.stop();
            }

            const newJob = cron.schedule(finalSchedule, async () => {
              secureLog('info', `Running edited automated task: ${taskNameToEdit}`);
              const randomPrompt = finalPrompts[Math.floor(Math.random() * finalPrompts.length)];
              try {
                await bot.sendChatAction(finalTargetGroupId, 'typing').catch(e => secureLog('error', `Error sending chat action to ${finalTargetGroupId} for ${taskNameToEdit}: ${e.message}`));
                const hustleResponse = await hustleClient.chat([{ role: 'user', content: randomPrompt }], {
                  vaultId: `autotask-${taskNameToEdit}`,
                  enableTools: true,
                  allowFunctionCalls: true,
                  toolboxAccess: 'full',
                  executionMode: 'auto',
                  maxToolCalls: 5,
                  toolTimeout: 30000
                });
                let messageToSend = typeof hustleResponse === 'string' ? hustleResponse : hustleResponse.content || 'No content from Hustle.';
                messageToSend = formatAsPlainText(messageToSend); // Convert to plain text

                // Use our explicitly captured CONFIG_FOR_TASKS
                messageToSend = typeof messageToSend !== 'string' ? 'Invalid message format' :
                                messageToSend.length <= CONFIG.MAX_MESSAGE_SIZE ? messageToSend :
                                messageToSend.slice(0, CONFIG.MAX_MESSAGE_SIZE - 3) + '...';
                
                await bot.sendMessage(finalTargetGroupId, messageToSend);
                secureLog('success', `Automated task "${taskNameToEdit}" executed. Prompt: "${randomPrompt}". Response sent to group ${finalTargetGroupId}.`);
              } catch (error) {
                secureLog('error', `Error executing automated task "${taskNameToEdit}": ${error.message}`);
                try {
                  await bot.sendMessage(chatId, `⚠️ Error executing automated task "${taskNameToEdit}": ${error.message}. Check logs.`);
                } catch (notifyError) {
                  secureLog('error', `Failed to send error notification for task ${taskNameToEdit}: ${notifyError.message}`);
                }
              }
            }, {
              scheduled: wasRunning, // Preserve previous running state
              timezone: process.env.TZ
            });

            scheduledTasks[taskNameToEdit] = {
              ...taskToEdit, // Preserve createdBy and other potential fields
              cronJob: newJob,
              schedule: finalSchedule,
              targetGroupId: finalTargetGroupId,
              prompts: finalPrompts,
              running: wasRunning
            };

            bot.sendMessage(chatId, `✅ Task "${taskNameToEdit}" updated successfully.\nChanges: ${updateMessages.join('; ')}`);
            secureLog('success', `Automated task "${taskNameToEdit}" edited by user ${msg.from.id}. Changes: ${updateMessages.join('; ')}`);
            saveTasksToFile(); // Save tasks after editing

          } catch (error) {
            bot.sendMessage(chatId, `❌ Error updating task "${taskNameToEdit}": ${error.message}`);
            secureLog('error', `Error updating task ${taskNameToEdit}: ${error.message}`);
            // Attempt to revert to the old job if possible or just log error
            // For simplicity, we are not attempting a revert here, but in a more complex system, one might.
          }
          break;

        default:
          if (!isMessageProcessed(msg)) {
            try {
              await bot.sendMessage(chatId, 'Unknown command. Try /help to see available commands.');
              secureLog('success', 'Unknown command response sent');
            } catch (error) {
              secureLog('error', 'Error sending unknown command response: ' + error.message);
            }
          }
      }
    }

    // Single message handler for all commands
    bot.on('message', async (msg) => {
      const text = msg.text;
      if (!text || !text.startsWith('/')) return;

      const [command, ...args] = text.split(' ');
      await handleCommand(msg, command.toLowerCase(), args.join(' '));
    });

    // Enhanced error handling for polling
    bot.on('polling_error', (error) => {
      secureLog('error', 'Polling error: ' + error.message);
      // Don't exit on polling errors, let the bot try to recover
    });

    bot.on('error', (error) => {
      secureLog('error', 'Bot error: ' + error.message);
    });

    console.log('\nBot is starting...');
    console.log('Available commands:');
    console.log('- /start');
    console.log('- /help');
    console.log('- /hustle [message]');
    console.log('- /hustlestream [message]');
    console.log('- /streamtest');
    console.log('\nAdmin Commands (if ADMIN_USER_IDS is set):');
    console.log('- /addautotask <name> <cron_schedule> <target_group_id> <prompt1> [prompt2...]');
    console.log('- /listautotasks');
    console.log('- /toggleautotask <name>');
    console.log('- /deleteautotask <name>');
    console.log('- /editautotask <name> [--schedule "new_schedule"] [--groupid <new_group_id>] [--prompts "prompt1" ...]');
    console.log('\n⏳ Connecting to Telegram...');
}); // End of loadTasksFromFile().then()

// Function to save tasks to a file
async function saveTasksToFile() {
  secureLog('info', 'Attempting to save tasks to file...');
  try {
    const tasksToSave = {};
    for (const taskName in scheduledTasks) {
      const task = scheduledTasks[taskName];
      tasksToSave[taskName] = {
        schedule: task.schedule,
        targetGroupId: task.targetGroupId,
        prompts: task.prompts,
        running: task.running,
        createdBy: task.createdBy
      };
    }
    const dataToWrite = JSON.stringify(tasksToSave, null, 2);
    secureLog('info', `Data to write to ${TASKS_FILE_PATH}:\n${dataToWrite}`);
    await fs.writeFile(TASKS_FILE_PATH, dataToWrite);
    secureLog('success', 'Scheduled tasks saved successfully to file.');
  } catch (error) {
    secureLog('error', `Failed to save tasks to file: ${error.message}`);
  }
}

// Function to load tasks from a file and re-initialize them
async function loadTasksFromFile() {
  secureLog('info', `Attempting to load tasks from ${TASKS_FILE_PATH}...`);
  try {
    const data = await fs.readFile(TASKS_FILE_PATH, 'utf8');
    secureLog('info', `Data read from ${TASKS_FILE_PATH}:\n${data}`);
    const loadedTasksConfig = JSON.parse(data);
    let reinitializedCount = 0;

    // Explicitly capture CONFIG and hustleClient in this scope for use by the cron jobs
    const CONFIG_FOR_TASKS = CONFIG;
    // const HUSTLE_CLIENT_FOR_TASKS = hustleClient; // Capture hustleClient // <<< This should now work as hustleClient is in scope

    for (const taskName in loadedTasksConfig) {
      const config = loadedTasksConfig[taskName];
      try {
        if (!cron.validate(config.schedule)) {
          secureLog('error', `Skipping task "${taskName}" from file: Invalid cron schedule "${config.schedule}".`);
          continue;
        }

        const job = cron.schedule(config.schedule, async () => {
          secureLog('info', `Running automated task (from file): ${taskName}`);
          const randomPrompt = config.prompts[Math.floor(Math.random() * config.prompts.length)];
          try {
            // Use the captured HUSTLE_CLIENT_FOR_TASKS
            await bot.sendChatAction(config.targetGroupId, 'typing').catch(e => secureLog('error', `Error sending chat action to ${config.targetGroupId} for ${taskName}: ${e.message}`));
            const hustleResponse = await hustleClient.chat([{ role: 'user', content: randomPrompt }], {
              vaultId: `autotask-${taskName}`,
              enableTools: true,
              allowFunctionCalls: true,
              toolboxAccess: 'full',
              executionMode: 'auto',
              maxToolCalls: 5,
              toolTimeout: 30000
            }); // Tool-enabled automated task
            let messageToSend = typeof hustleResponse === 'string' ? hustleResponse : hustleResponse.content || 'No content from Hustle.';
            messageToSend = formatAsPlainText(messageToSend); // Convert to plain text

            messageToSend = typeof messageToSend !== 'string' ? 'Invalid message format' :
                            messageToSend.length <= CONFIG_FOR_TASKS.MAX_MESSAGE_SIZE ? messageToSend :
                            messageToSend.slice(0, CONFIG_FOR_TASKS.MAX_MESSAGE_SIZE - 3) + '...';
            
            await bot.sendMessage(config.targetGroupId, messageToSend);
            secureLog('success', `Automated task "${taskName}" (from file) executed. Prompt: "${randomPrompt}". Response sent to group ${config.targetGroupId}.`);
          } catch (execError) {
            secureLog('error', `Error executing automated task "${taskName}" (from file): ${execError.message}`);
          }
        }, {
          scheduled: config.running,
          timezone: process.env.TZ
        });

        scheduledTasks[taskName] = {
          cronJob: job,
          schedule: config.schedule,
          targetGroupId: config.targetGroupId,
          prompts: config.prompts,
          running: config.running,
          createdBy: config.createdBy
        };
        if (config.running) {
           // job.start(); // Already handled by scheduled: true in cron.schedule if config.running is true
        } else {
           // job.stop(); // Already handled by scheduled: false in cron.schedule if config.running is false
        }
        reinitializedCount++;
      } catch (taskInitError) {
        secureLog('error', `Error re-initializing task "${taskName}" from file: ${taskInitError.message}`);
      }
    }
    if (reinitializedCount > 0) {
        secureLog('info', `Successfully loaded and re-initialized ${reinitializedCount} tasks from file.`);
    }
  } catch (error) {
    if (error.code === 'ENOENT') {
      secureLog('info', `No tasks file found (${TASKS_FILE_PATH}). Starting with no scheduled tasks.`);
    } else {
      secureLog('error', `Failed to load tasks from file: ${error.message}`);
      secureLog('error', `Error details during load: ${JSON.stringify(error)}`);
    }
  }
} 